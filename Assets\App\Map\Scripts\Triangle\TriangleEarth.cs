using UnityEngine;
using System;

public class TriangleEarth : MonoBehaviour
{
    [SerializeField] private Material wood;
    [SerializeField] private Material stone;
    [SerializeField] private Material water;
    [SerializeField] private Material fallback;
    [SerializeField] private MeshRenderer rendererComponent;

    private Material activeMaterial;
    private ResourceType activeResource;
    private static readonly Array resourceTypeValues = Enum.GetValues(typeof(ResourceType));

    public bool Highlighted
    {
        set
        {
            if (value)
            {
                if (activeMaterial == null) activeMaterial = rendererComponent.materials[0];
                // Add the outline material as the second material
                rendererComponent.materials = new Material[] { activeMaterial };
            }
            else
            {
                // Remove the outline material and revert to the active material
                rendererComponent.materials = new Material[] { activeMaterial != null ? activeMaterial : fallback };
                activeMaterial = null;
            }
        }
    }

    public void PickRandomResource()
    {
        Resource = (ResourceType)resourceTypeValues.GetValue(UnityEngine.Random.Range(0, resourceTypeValues.Length));
    }

    public ResourceType Resource
    {
        get
        {
            return activeResource;
        }
        set
        {
            if (activeResource == value) return;

            activeResource = value;

            Material newMaterial = activeResource switch
            {
                ResourceType.Wood => wood,
                ResourceType.Stone => stone,
                ResourceType.Water => water,
                _ => fallback,
            };

            // Update the primary material while keeping the outline intact
            activeMaterial = newMaterial;
            rendererComponent.materials = new Material[] { newMaterial };
        }
    }
}