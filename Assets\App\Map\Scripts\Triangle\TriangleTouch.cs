using UnityEngine;
using UnityEngine.EventSystems;

public class TriangleTouch : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler
{
    [SerializeField] private Collider colliderComponent;
    public delegate void OnInteract();
    private OnInteract onInteract;
    private bool _interactable = false;

    public bool Interactable
    {
        get => _interactable;
        set
        {
            _interactable = value;
            colliderComponent.enabled = value; // Enable collider only when interactable
        }
    }

    void Awake()
    {
        Interactable = false;
    }

    public void AddOnInteract(OnInteract onInteract)
    {
        this.onInteract += onInteract;
    }
    public void RemoveOnInteract(OnInteract onInteract)
    {
        this.onInteract -= onInteract;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if (!Interactable) return;
        Debug.Log("You just tapped an element");
        onInteract?.Invoke();
    }
}
